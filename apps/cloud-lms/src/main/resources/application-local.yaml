spring:
  main:
    lazy-initialization: true
  application:
    set-additional-jvm-arguments:
      - "-Duser.timezone=Asia/Kolkata"
  datasource :
    url: *********************************************************
    username: wexl_app_admin
    password: QBYYhJJuibIWODGF
    hikari:
      maximum-pool-size: 2
  liquibase:
    enabled: false
  jpa:
    hibernate:
      ddl-auto: none
    show-sql: true
    properties:
      hibernate:
        generate_statistics: true
server:
  port: 8070
  servlet:
    context-path: /api
app:
  orgs:
    deletion:
      enabled: false
  publicDomainUrl: http://localhost:8070/
  betDomainUrl:  https://bet.academyteacher.com/
  localeFilesLocation: file:///home/<USER>/repos/wexl-apps-i18n/i18n/
  shinkan:
    enabled: true
    url: http://api.qria.co.in/api/Applicant/GetReportStatus
    apiKey: 1d7b229b-8143-4dd3-85ae-9ffcc2f7ce0e
  sns:
    topicArn: arn:aws:sns:ap-south-1:473077116069:wexl-retail-notifications-nonprod
  wordFinderUrl: https://api.dictionaryapi.dev/api/v2/entries/en/
  batch:
    url: https://learn.academyteacher.com/api/batch/job
  storageBucket: wexl-student-info-wasabi-nonprod
  storage: wexledu-wasabi-nonprod
  parseable:
    streamName: NR
    enabled: false
  curriculum:
    eduBoards: ol-elp,english-obs-gulmohar,bur-elp,ilp-elp,rsgrle-elp,head-word-elp,rsgrrd-elp,blue-bells,nios,nm-tnsdc-elp,cbse-lean-hyphen,pearson-elp,iit-jee-neet-foundation,iit-neet-foundation,stdb,bet,cbse,icse,bset,bj,cbse-elp,ratna-sagar-elp,als-elp,xseed-elp,evershine-elp,intellica-elp,ckds-elp,elp-refer,elp,cbse-ncert-elp,pearson-elp,eupheus-elp,wonder-kid-elp,bset-elp,bset-new-elp
  admission:
    location: fghj,friend,Tm,Wexm,12,x
  email:
    scheduler: true
    smtpConfig:
      nal635825:
        host: email-smtp.ap-south-1.amazonaws.com
        port: 587
        username: AKIAW4JMX5SSYG6LE4OC
        password: BHC+HlnuRCE28wD7j2UiJFLpybntOY/QWKvy02Cp/g3f
        fromEmail: <EMAIL>
        authKey: 386992AIQn3Fech63a01d0aP1
        integratedNumber: 918520955547

  allowed:
    test:
      orgSlug: nal635825
logging:
  level:
    com:
      wexl: DEBUG
    org:
      hibernate:
        sql: DEBUG
        tool.hbm2ddl: DEBUG
      springframework:
        boot:
          autoconfigure: DEBUG
        web: ERROR
        servlet:
          handler: ERROR
    org.apache.fop: OFF
urls:
  content: https://learn.academyteacher.com/api/content/
  correction: https://learn.academyteacher.com/api/correction/
  dpsIntegrationService: http://localhost:8080
langchain4j:
  open-ai:
    chat-model:
      api-key: ********************************************************************************************************************************************************************
      model-name: gpt-4o
      temperature: 0.7
      timeout: PT3M
      log-requests: false
      log-responses: false
      response-format: json_schema
