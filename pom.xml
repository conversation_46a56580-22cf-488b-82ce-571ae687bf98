<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-parent</artifactId>
    <version>3.2.3</version>
  </parent>

  <groupId>com.wexl</groupId>
  <artifactId>wexl-lms-apps</artifactId>
  <version>1.2-SNAPSHOT</version>
  <packaging>pom</packaging>
  <name>WeXL LMS Apps</name>

  <modules>
    <module>apps</module>
    <module>libs</module>
  </modules>

  <properties>
    <java.version>21</java.version>
    <aws.java.sdk.version>1.12.195</aws.java.sdk.version>
    <commons-io.version>2.11.0</commons-io.version>
    <spring-jdbc.version>5.3.25</spring-jdbc.version>
    <jjwt.version>0.11.5</jjwt.version>
    <modelmapper.version>2.4.3</modelmapper.version>
    <nimbus-jose-jwt.version>9.24.4</nimbus-jose-jwt.version>
    <javassist.version>3.27.0-GA</javassist.version>
    <core.version>3.3.0</core.version>
    <javase.version>3.3.0</javase.version>
    <pdfbox.version>2.0.26</pdfbox.version>
    <mapstruct.version>1.5.2.Final</mapstruct.version>
    <mapstruct-processor.version>1.5.2.Final</mapstruct-processor.version>
    <libphonenumber.version>8.12.10</libphonenumber.version>
    <annotationeer-exporter.version>1.0.5503</annotationeer-exporter.version>
    <commons-collections4.version>4.4</commons-collections4.version>
    <commons-text.version>1.10.0</commons-text.version>
    <json.version>20220924</json.version>
    <commons-csv.version>1.9.0</commons-csv.version>
    <java-telegram-bot-api.version>5.2.0</java-telegram-bot-api.version>
    <batik-all.version>1.14</batik-all.version>
    <bcprov-jdk15on.version>1.69</bcprov-jdk15on.version>
    <jets3t.version>0.9.4</jets3t.version>
  </properties>
  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>net.javacrumbs.shedlock</groupId>
        <artifactId>shedlock-bom</artifactId>
        <version>6.6.1</version>  <!-- latest as of 09 May 2025 -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
    </dependencies>
  </dependencyManagement>
  <repositories>
    <repository>
      <id>wexl-artifactory</id>
      <url>https://pkgs.dev.azure.com/wexl/_packaging/wexl-artifactory/maven/v1</url>
      <releases>
        <enabled>true</enabled>
      </releases>
      <snapshots>
        <enabled>true</enabled>
      </snapshots>
    </repository>
  </repositories>
  <distributionManagement>
    <repository>
      <id>wexl-artifactory</id>
      <url>https://pkgs.dev.azure.com/wexl/_packaging/wexl-artifactory/maven/v1</url>
      <releases>
        <enabled>true</enabled>
      </releases>
      <snapshots>
        <enabled>true</enabled>
      </snapshots>
    </repository>
  </distributionManagement>

  <dependencies>
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-annotations</artifactId>
    </dependency>
    <dependency>
      <groupId>com.github.javafaker</groupId>
      <artifactId>javafaker</artifactId>
      <version>1.0.2</version>
    </dependency>
  </dependencies>

  <build>
    <plugins>
      <plugin>
        <groupId>com.spotify.fmt</groupId>
        <artifactId>fmt-maven-plugin</artifactId>
        <version>2.23</version>
        <executions>
          <execution>
            <phase>process-sources</phase>
            <goals>
              <goal>check</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.openrewrite.maven</groupId>
        <artifactId>rewrite-maven-plugin</artifactId>
        <version>5.24.0</version>
        <configuration>
          <activeRecipes>
            <recipe>org.openrewrite.java.migrate.UpgradeToJava21</recipe>
          </activeRecipes>
        </configuration>
        <dependencies>
          <dependency>
            <groupId>org.openrewrite.recipe</groupId>
            <artifactId>rewrite-migrate-java</artifactId>
            <version>2.10.0</version>
          </dependency>
        </dependencies>
      </plugin>
      <!--            <plugin>-->
      <!--                <artifactId>maven-enforcer-plugin</artifactId>-->
      <!--                <executions>-->
      <!--                    <execution>-->
      <!--                        <goals>-->
      <!--                            <goal>enforce</goal>-->
      <!--                        </goals>-->
      <!--                        <configuration>-->
      <!--                            <rules>-->
      <!--                                <dependencyConvergence/>-->
      <!--                            </rules>-->
      <!--                        </configuration>-->
      <!--                    </execution>-->
      <!--                </executions>-->
      <!--            </plugin>-->
    </plugins>
  </build>
</project>
