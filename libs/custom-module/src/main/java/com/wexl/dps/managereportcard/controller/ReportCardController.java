package com.wexl.dps.managereportcard.controller;

import com.wexl.dps.managereportcard.service.ManageReportCardService;
import com.wexl.retail.commons.security.annotation.IsStudent;
import com.wexl.retail.offlinetest.dto.ReportCard;
import com.wexl.retail.offlinetest.model.ReportCardTemplateType;
import com.wexl.retail.reportcards.dto.ReportCardConfigDto;
import com.wexl.retail.term.dto.TermDto;
import java.io.IOException;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequiredArgsConstructor
@RequestMapping("/orgs/{orgSlug}")
public class ReportCardController {
  private final ManageReportCardService reportCardService;

  @PostMapping("/report-card-configs")
  public void createReportCard(
      @PathVariable("orgSlug") String orgSlug,
      @RequestBody ReportCardConfigDto.ReportCardRequest reportCardRequest) {
    reportCardService.createReportCard(orgSlug, reportCardRequest);
  }

  @GetMapping("/report-card-configs")
  public ReportCardConfigDto.ReportCardJobDetails getReportCardConfigs(
      @PathVariable("orgSlug") String orgSlug) {
    return reportCardService.getReportCardConfigs(orgSlug);
  }

  @GetMapping("/report-card-configs/{reportCardConfigId}")
  public ReportCardConfigDto.ReportCardConfigResponse getReportCardConfigDetails(
      @PathVariable("reportCardConfigId") Long reportCardConfigId) {
    return reportCardService.getReportCardConfigDetailsByReportCardConfigId(reportCardConfigId);
  }

  @PutMapping("/report-card-configs/{reportCardConfigId}")
  public void updateReportCardConfig(
      @PathVariable("reportCardConfigId") Long id,
      @RequestBody ReportCardConfigDto.ReportCardRequest request) {
    reportCardService.updateReportCardConfig(id, request);
  }

  @DeleteMapping("/report-card-configs/{reportCardConfigId}")
  public void deleteReportCardConfigById(@PathVariable("reportCardConfigId") Long id) {
    reportCardService.deleteReportCardConfigById(id);
  }

  @PutMapping("/report-card-config-details/{reportCardConfigDetailId}")
  public void updateTestDetails(
      @PathVariable("reportCardConfigDetailId") Long id,
      @RequestBody ReportCardConfigDto.TermRequest request) {
    reportCardService.updateReportCardConfigDetail(id, request);
  }

  @DeleteMapping("/report-card-config-details/{reportCardConfigDetailId}")
  public void deleteReportCardConfigDetail(@PathVariable("reportCardConfigDetailId") Long id) {
    reportCardService.deleteReportCardConfigDetailById(id);
  }

  @PostMapping("/report-card-config-details/{configDetailId}")
  public void reportCardAssessmentEvaluationConfig(
      @PathVariable("configDetailId") Long configDetailId,
      @RequestBody ReportCardConfigDto.MergeAssessmentCategoryRequest request) {
    reportCardService.reportCardAssessmentEvaluationConfig(configDetailId, request);
  }

  @GetMapping("/term-assessment-categories")
  public List<TermDto.TermAssessmentCategoryDetails> getTermAssessmentCategories(
      @PathVariable("orgSlug") String orgSlug,
      @RequestParam Long reportCardConfigId,
      @RequestParam Long termAssessmentId) {
    return reportCardService.getSelectedAssessmentCategories(
        orgSlug, reportCardConfigId, termAssessmentId);
  }

  @IsStudent
  @GetMapping(
      value = "/students/{studentAuthId}/admit-card",
      produces = MediaType.APPLICATION_PDF_VALUE)
  public byte[] getStudentAdmitCard(
      @PathVariable("orgSlug") String orgSlug,
      @PathVariable("studentAuthId") String studentAuthId,
      @RequestParam("test_definition_id") Long testDefinitionId) {
    return reportCardService.getStudentAdmitCard(orgSlug, studentAuthId, testDefinitionId);
  }

  @IsStudent
  @GetMapping(
      value = "/students/{studentAuthId}/pallavi-admit-card",
      produces = MediaType.APPLICATION_PDF_VALUE)
  public byte[] getPallaviStudentAdmitCard(
      @PathVariable("orgSlug") String orgSlug,
      @PathVariable("studentAuthId") String studentAuthId,
      @RequestParam("test_definition_id") Long testDefinitionId) {
    return reportCardService.getPallaviStudentAdmitCard(orgSlug, studentAuthId, testDefinitionId);
  }

  @IsStudent
  @GetMapping(
      value = "/students/{studentAuthId}/ey-admit-card",
      produces = MediaType.APPLICATION_PDF_VALUE)
  public byte[] getDpsStudentEyAdmitCard(
      @PathVariable("orgSlug") String orgSlug,
      @PathVariable("studentAuthId") String studentAuthId,
      @RequestParam("test_definition_id") Long testDefinitionId) {
    return reportCardService.getDpsStudentEyAdmitCard(orgSlug, studentAuthId, testDefinitionId);
  }

  @IsStudent
  @GetMapping(
      value = "/students/{studentAuthId}/report-card",
      produces = MediaType.APPLICATION_PDF_VALUE)
  public byte[] getStudentReportCard(
      @PathVariable("orgSlug") String orgSlug,
      @RequestParam("template_type") ReportCardTemplateType templateType,
      @RequestParam(value = "template_id", required = false) Long templateId,
      @RequestParam(value = "test_definition_id", required = false) Long testDefinitionId,
      @RequestParam(value = "term_slug", required = false) List<String> termSlugs) {
    return reportCardService.getStudentReportCard(
        orgSlug, termSlugs, testDefinitionId, templateType, templateId);
  }

  @IsStudent
  @GetMapping("/students/{studentAuthId}/exam-report-cards")
  public ReportCard.StudentReportCards getStudentShowReport(
      @PathVariable String orgSlug, @PathVariable("studentAuthId") String studentAuthId) {

    return reportCardService.getAllStudentReportCards(orgSlug, studentAuthId);
  }

  @PostMapping("/students-marks-list")
  public ResponseEntity<byte[]> getStudentMarksList(
      @PathVariable String orgSlug, @RequestBody ReportCard.StudentMarksRequest request)
      throws IOException {

    return reportCardService.downloadStudentMarksList(orgSlug, request);
  }

  @PostMapping("/download-grade-counts")
  public ResponseEntity<byte[]> downloadGradeCounts(
      @PathVariable String orgSlug, @RequestBody ReportCard.StudentMarksRequest request)
      throws IOException {

    return reportCardService.downloadGradeCounts(
        orgSlug, request.boardSlug(), request.gradeSlug(), request.subjectSlug());
  }
}
