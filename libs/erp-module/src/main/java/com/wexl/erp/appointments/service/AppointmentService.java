package com.wexl.erp.appointments.service;

import com.wexl.erp.appointments.dto.AppointmentSearchDto;
import com.wexl.erp.appointments.dto.ParentAppointmentDto;
import com.wexl.erp.appointments.model.Appointment;
import com.wexl.erp.appointments.model.AppointmentStatus;
import com.wexl.erp.appointments.repository.AppointmentRepository;
import com.wexl.retail.auth.UserRoleHelper;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.util.DateTimeUtil;
import com.wexl.retail.guardian.model.Guardian;
import com.wexl.retail.guardian.repository.GuardianRepository;
import com.wexl.retail.guardian.service.GuardianService;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.User;
import com.wexl.retail.notification.service.EventNotificationService;
import com.wexl.retail.notifications.dto.NotificationDto;
import com.wexl.retail.notifications.dto.NotificationType;
import com.wexl.retail.notifications.service.NotificationsService;
import com.wexl.retail.repository.StudentRepository;
import com.wexl.retail.repository.TeacherRepository;
import com.wexl.retail.repository.UserRepository;
import com.wexl.retail.section.domain.Section;
import com.wexl.retail.section.repository.TeacherSectionRepository;
import com.wexl.retail.staff.repository.StaffRepository;
import com.wexl.retail.telegram.service.UserService;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import lombok.EqualsAndHashCode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@EqualsAndHashCode
@Service
@Slf4j
@RequiredArgsConstructor
public class AppointmentService {

  private final AppointmentRepository appointmentsRepository;
  private final NotificationsService notificationsService;
  private final DateTimeUtil dateTimeUtil;
  private final TeacherSectionRepository teacherSectionRepository;
  private final StudentRepository studentRepository;
  private final UserRepository userRepository;
  private final GuardianRepository guardianRepository;
  private final UserService userService;
  private final StaffRepository staffRepository;
  private final GuardianService guardianService;
  private final EventNotificationService eventNotificationService;
  private final TeacherRepository teacherRepository;

  private final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd-MM-yyyy");
  private static String designation;

  private ParentAppointmentDto.Response buildAppointmentResponse(Appointment appointment) {
    Optional<User> user = userRepository.findByAuthUserId(appointment.getReviewedBy());
    return ParentAppointmentDto.Response.builder()
        .appointmentId(appointment.getId())
        .studentId(appointment.getStudent().getId())
        .guardianName(
            appointment.getGuardian() != null
                ? appointment.getGuardian().getFirstName()
                    + " "
                    + appointment.getGuardian().getLastName()
                : null)
        .guardianId(appointment.getGuardian().getId())
        .studentName(userService.getNameByUserInfo(appointment.getStudent().getUserInfo()))
        .gradeName(appointment.getStudent().getSection().getGradeName())
        .gradeSlug(appointment.getStudent().getSection().getGradeSlug())
        .studentSection(appointment.getStudent().getSection().getName())
        .recipientName(appointment.getRecipientName())
        .role(appointment.getRole())
        .appointmentDate(DateTimeUtil.convertIso8601ToEpoch(appointment.getAppointmentDate()))
        .appointmentReason(appointment.getAppointmentReason())
        .accountHolder(designation)
        .status(appointment.getStatus())
        .appliedDate(DateTimeUtil.convertIso8601ToEpoch(appointment.getAppliedDate()))
        .reviewedBy(
            user.isPresent() ? user.get().getFirstName() + " " + user.get().getLastName() : null)
        .reviewedOn(
            appointment.getReviewedOn() != null
                ? DateTimeUtil.convertIso8601ToEpoch(appointment.getReviewedOn())
                : null)
        .build();
  }

  private Appointment buildAppointmentRequest(ParentAppointmentDto.Request request) {
    return Appointment.builder()
        .appointmentDate(dateTimeUtil.convertEpochToIso8601(request.appointmentDate()))
        .appointmentReason(request.appointmentReason())
        .recipientName(request.recipientName())
        .role(request.role())
        .status(AppointmentStatus.PENDING)
        .appliedDate(LocalDateTime.now())
        .build();
  }

  @Transactional
  public ParentAppointmentDto.Response applyForAppointment(
      String orgSlug, String guardianAuthId, ParentAppointmentDto.Request request) {

    User user = guardianService.validateUser(guardianAuthId);
    Student student = user.getStudentInfo();
    if (student == null) {
      throw new ApiException(InternalErrorCodes.NO_RECORD_FOUND, "Student not found");
    }

    Guardian guardian = getValidatedGuardianForStudent(student);

    Appointment appointment = buildAppointmentRequest(request);
    appointment.setStudent(student);
    appointment.setGuardian(guardian);
    appointment.setOrgSlug(orgSlug);
    Appointment savedAppointment = appointmentsRepository.save(appointment);

    String orgAdminForOrganization =
        userRepository.getOrgAdminForOrganization(orgSlug).getAuthUserId();

    HashMap<String, String> userNames =
        (HashMap<String, String>)
            userRepository.getUserByAuthUserIdIn(request.authId()).stream()
                .collect(
                    Collectors.toMap(
                        User::getAuthUserId, u -> u.getFirstName() + " " + u.getLastName()));

    LocalDateTime appliedDate = LocalDateTime.now();

    String notificationMessage =
        guardian.getFirstName()
            + " "
            + guardian.getLastName()
            + " has requested an appointment for "
            + student.getUserInfo().getFirstName()
            + " "
            + student.getUserInfo().getLastName()
            + " on "
            + appointment.getAppointmentDate().format(formatter)
            + ". Reason: "
            + appointment.getAppointmentReason()
            + ". Applied on: "
            + appliedDate.format(formatter);

    String notificationMessageForOrgAdmin =
        guardian.getFirstName()
            + " "
            + guardian.getLastName()
            + " has requested an appointment for "
            + student.getUserInfo().getFirstName()
            + " "
            + student.getUserInfo().getLastName()
            + " with "
            + String.join(", ", userNames.values())
            + " on "
            + appointment.getAppointmentDate().format(formatter)
            + ". Reason: "
            + appointment.getAppointmentReason()
            + ". Applied on: "
            + appliedDate.format(formatter);

    sendNotificationToAdmin(
        orgAdminForOrganization, orgSlug, notificationMessageForOrgAdmin, appliedDate);

    for (String authId : request.authId()) {
      if (request.role().equalsIgnoreCase("TEACHER")) {
        NotificationDto.NotificationRequest notificationRequest =
            NotificationDto.NotificationRequest.builder()
                .title("New Appointment Request")
                .message(notificationMessage)
                .notificationType(NotificationType.APPOINTMENT_REQUEST)
                .staffAuthId(authId)
                .build();

        if (request.authId() != null && !request.authId().isEmpty()) {
          notificationsService.createNotificationByTeacher(
              orgSlug,
              notificationRequest,
              authId,
              false,
              dateTimeUtil.convertLocalTimeToEpoch(LocalTime.from(appliedDate)));
          eventNotificationService.sendPushNotificationForUser(
              authId,
              notificationRequest.message(),
              orgSlug,
              NotificationType.APPOINTMENT_REQUEST,
              "Appointment Notification");
        }
      }
    }
    for (String authId : request.authId()) {
      if (request.role().equalsIgnoreCase("STAFF")) {
        NotificationDto.NotificationRequest notificationRequest =
            NotificationDto.NotificationRequest.builder()
                .title("New Appointment Request")
                .message(notificationMessage)
                .staffAuthId(authId)
                .notificationType(NotificationType.STAFF_APPOINTMENT)
                .build();

        if (request.authId() != null && !request.authId().isEmpty()) {
          notificationsService.createNotificationByStaff(
              orgSlug, notificationRequest, authId, false);
          eventNotificationService.sendPushNotificationForUser(
              authId,
              notificationRequest.message(),
              orgSlug,
              NotificationType.APPOINTMENT_REQUEST,
              "Appointment Notification");
        }
      }
    }

    log.info("Appointment request created with ID: {}", savedAppointment.getId());
    return buildAppointmentResponse(savedAppointment);
  }

  public void sendNotificationToAdmin(
      String authId, String orgSlug, String notificationMessage, LocalDateTime appliedDate) {
    NotificationDto.NotificationRequest notificationRequest =
        NotificationDto.NotificationRequest.builder()
            .title("New Appointment Request")
            .message(notificationMessage)
            .notificationType(NotificationType.APPOINTMENT_REQUEST)
            .staffAuthId(authId)
            .build();

    notificationsService.createNotificationByTeacher(
        orgSlug,
        notificationRequest,
        authId,
        false,
        dateTimeUtil.convertLocalTimeToEpoch(LocalTime.from(appliedDate)));
    eventNotificationService.sendPushNotificationForUser(
        authId,
        notificationRequest.message(),
        orgSlug,
        NotificationType.APPOINTMENT_REQUEST,
        "Appointment Notification");
  }

  public List<ParentAppointmentDto.Response> getGuardianAppointmentRequests(
      String orgSlug, String guardianAuthId, String role) {

    User user = guardianService.validateUser(guardianAuthId);

    Student student = user.getStudentInfo();
    if (student == null) {
      throw new ApiException(InternalErrorCodes.NO_RECORD_FOUND, "Student not found");
    }

    Guardian guardian = getValidatedGuardianForStudent(student);

    List<Appointment> appointments =
        role == null
            ? appointmentsRepository.findByOrgSlugAndGuardianOrderByAppliedDateDescStatusAsc(
                orgSlug, guardian)
            : appointmentsRepository.findByOrgSlugAndGuardianAndRoleOrderByAppliedDateDescStatusAsc(
                orgSlug, guardian, role);

    return appointments.stream().map(this::buildAppointmentResponse).collect(Collectors.toList());
  }

  @Transactional
  public ParentAppointmentDto.Response updateAppointmentRequest(
      String orgSlug,
      String guardianAuthId,
      Long appointmentId,
      ParentAppointmentDto.Request request) {

    Appointment appointment = getAppointmentById(appointmentId);

    User user = guardianService.validateUser(guardianAuthId);

    Student student = user.getStudentInfo();
    if (student == null) {
      throw new ApiException(InternalErrorCodes.NO_RECORD_FOUND, "Student not found");
    }

    Guardian guardian = getValidatedGuardianForStudent(student);
    if (guardian == null) {
      throw new ApiException(
          InternalErrorCodes.NO_RECORD_FOUND, "Guardian not found for this student");
    }

    appointment.setAppointmentDate(dateTimeUtil.convertEpochToIso8601(request.appointmentDate()));
    appointment.setAppointmentReason(request.appointmentReason());

    Appointment updatedAppointment = appointmentsRepository.save(appointment);
    return buildAppointmentResponse(updatedAppointment);
  }

  @Transactional
  public void deleteAppointmentRequest(Long appointmentId) {
    appointmentsRepository.deleteById(appointmentId);
  }

  @Transactional
  public void approveOrRejectAppointmentRequest(
      String orgSlug,
      String teacherAuthId,
      Long appointmentId,
      ParentAppointmentDto.ApprovalRequest approvalRequest) {

    Appointment appointment = getAppointmentById(appointmentId);

    appointment.setStatus(approvalRequest.status());
    appointment.setReviewedBy(teacherAuthId);
    appointment.setReviewedOn(LocalDateTime.now());

    appointmentsRepository.save(appointment);

    NotificationDto.NotificationRequest notificationRequest =
        NotificationDto.NotificationRequest.builder()
            .title("Appointment Request " + approvalRequest.status())
            .userAuthId(appointment.getStudent().getUserInfo().getAuthUserId())
            .message(
                "Your appointment request for "
                    + appointment.getStudent().getUserInfo().getFirstName()
                    + " "
                    + appointment.getStudent().getUserInfo().getLastName()
                    + " on "
                    + appointment.getAppointmentDate().format(formatter)
                    + " has been "
                    + approvalRequest.status().toString().toLowerCase())
            .notificationType(
                approvalRequest.status() == AppointmentStatus.APPROVED
                    ? NotificationType.APPOINTMENT_APPROVED
                    : NotificationType.APPOINTMENT_DISAPPROVED)
            .studentIds(List.of(appointment.getStudent().getId()))
            .build();

    notificationsService.createNotificationByUser(
        orgSlug, notificationRequest, appointment.getStudent().getUserInfo().getAuthUserId());
    eventNotificationService.sendPushNotificationForUser(
        appointment.getStudent().getUserInfo().getAuthUserId(),
        notificationRequest.message(),
        orgSlug,
        NotificationType.APPOINTMENT_REQUEST,
        "Appointment Notification");
  }

  public List<ParentAppointmentDto.Response> getAppointmentRequestsByStudentName(
      String searchKey, AppointmentSearchDto.Request request, String authUserId, String orgSlug) {
    List<User> matchingUsers =
        userRepository.findByOrgSlugAndSearchKey(orgSlug, "%" + searchKey + "%");
    User authUser = guardianService.validateUser(authUserId);

    boolean isAdmin = UserRoleHelper.get().isOrgAdmin(authUser);
    List<Long> teacherSectionList =
        isAdmin
            ? null
            : authUser.getTeacherInfo().getSections().stream().map(Section::getId).toList();

    List<Student> students =
        matchingUsers.stream()
            .filter(user -> user.getStudentInfo() != null)
            .map(User::getStudentInfo)
            .toList();

    List<Appointment> appointments =
        students.stream()
            .flatMap(student -> appointmentsRepository.findByStudent(student).stream())
            .collect(Collectors.toList());

    if (!isAdmin && teacherSectionList != null) {
      appointments =
          appointments.stream()
              .filter(
                  appointment ->
                      teacherSectionList.contains(appointment.getStudent().getSection().getId()))
              .collect(Collectors.toList());
    }

    return appointments.stream().map(this::buildAppointmentResponse).collect(Collectors.toList());
  }

  public List<ParentAppointmentDto.Response> getAllAppointmentRequestsSorted(
      String authUserId, String gradeSlug, String sectionName) {
    var user = guardianService.validateUser(authUserId);
    if (UserRoleHelper.get().isOrgAdmin(user)) {
      return appointmentsRepository.findAllByOrderByAppliedDateDescStatusAsc().stream()
          .map(this::buildAppointmentResponse)
          .filter(
              response ->
                  (Objects.isNull(gradeSlug) || Objects.isNull(sectionName))
                      || (response.gradeSlug().equalsIgnoreCase(gradeSlug)
                          && response.studentSection().equalsIgnoreCase(sectionName)))
          .collect(Collectors.toList());
    } else {
      List<Long> teacherSectionIds =
          user.getTeacherInfo().getSections().stream().map(Section::getId).toList();
      List<Appointment> pendingRequests =
          appointmentsRepository.findBySectionIds(teacherSectionIds);
      return pendingRequests.stream()
          .map(this::buildAppointmentResponse)
          .filter(
              response ->
                  (Objects.isNull(gradeSlug) || Objects.isNull(sectionName))
                      || (response.gradeSlug().equalsIgnoreCase(gradeSlug)
                          && response.studentSection().equalsIgnoreCase(sectionName)))
          .collect(Collectors.toList());
    }
  }

  public Appointment getAppointmentById(Long appointmentId) {
    return appointmentsRepository
        .findById(appointmentId)
        .orElseThrow(
            () ->
                new ApiException(
                    InternalErrorCodes.NO_RECORD_FOUND, "Appointment request not found"));
  }

  public Guardian getValidatedGuardianForStudent(Student student) {
    Guardian guardian = guardianRepository.findByStudentAndIsPrimary(student, true);

    if (guardian == null) {
      throw new ApiException(
          InternalErrorCodes.NO_RECORD_FOUND, "Guardian not found for this student");
    }

    if (guardian.getStudent().getId() != (student.getId())) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST, "Guardian is not related to this student");
    }

    return guardian;
  }

  public AppointmentSearchDto.Response getTeachersOrStaff(
      String orgSlug, String studentAuthId, String role) {

    User user = userRepository.getUserByAuthUserId(studentAuthId);
    Student student = user.getStudentInfo();
    List<Long> studentIds = List.of(student.getId());
    if (role.equalsIgnoreCase("TEACHER")) {

      var teachers = teacherRepository.findTeacherByStudentIds(studentIds, orgSlug);

      return AppointmentSearchDto.Response.builder()
          .staffResponse(Collections.emptyList())
          .teacherResponse(
              teachers.stream()
                  .map(
                      teacher ->
                          AppointmentSearchDto.TeacherResponse.builder()
                              .name(
                                  teacher.getUserInfo().getFirstName()
                                      + " "
                                      + teacher.getUserInfo().getLastName())
                              .userId(teacher.getId())
                              .authUserId(teacher.getUserInfo().getAuthUserId())
                              .build())
                  .toList())
          .build();
    }

    if (role.equalsIgnoreCase("STAFF")) {
      var staffs = staffRepository.getStaffByOrg(orgSlug);
      return AppointmentSearchDto.Response.builder()
          .staffResponse(
              staffs.stream()
                  .map(
                      staff ->
                          AppointmentSearchDto.StaffResponse.builder()
                              .authUserId(staff.getUser().getAuthUserId())
                              .fullName(
                                  staff.getUser().getFirstName()
                                      + " "
                                      + staff.getUser().getLastName())
                              .roles(
                                  staff.getRole().stream()
                                      .map(
                                          staffRole ->
                                              AppointmentSearchDto.StaffRole.builder()
                                                  .roleName(staffRole.getName())
                                                  .role(staffRole.getTemplate())
                                                  .build())
                                      .toList())
                              .build())
                  .toList())
          .teacherResponse(Collections.emptyList())
          .build();
    }
    throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "Invalid role specified: " + role);
  }
}
