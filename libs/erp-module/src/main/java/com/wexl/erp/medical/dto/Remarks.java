package com.wexl.erp.medical.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Remarks {
  private ActivityStatus dance;
  private ActivityStatus aerobics;
  private ActivityStatus yoga;
  private ActivityStatus pt;
  private ActivityStatus games;
  private ActivityStatus camps;
  private ActivityStatus treks;
  private ActivityStatus athletics;
  private ActivityStatus longDistanceRuns;
  private ActivityStatus gymnastics;
}
