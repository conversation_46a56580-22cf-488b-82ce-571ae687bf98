package com.wexl.erp.medical.service;

import com.wexl.erp.medical.MedicalHistory;
import com.wexl.erp.medical.dto.Allergies;
import com.wexl.erp.medical.dto.MedicalProfile;
import com.wexl.erp.medical.repository.MedicalRepository;
import com.wexl.retail.model.User;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class MedicalService {

    private final MedicalRepository medicalRepository;

    private MedicalHistory buildMedicalHistory(MedicalProfile.Request request) {
        return MedicalHistory.builder()
            .student(request.studentId())
            .bloodGroup(request.bloodGroup())
            .height(request.height())
            .weight(request.weight())
            .drugOrMedicines(request.drugOrMedicines())
            .foodAllergy(request.foodAllergy())
            .allergies(request.allergies())
            .illness(request.illness())
            .chronicDiseases(request.chronicDiseases())
            .heartCondition(request.heartCondition())
            .surgeryOrAdmittedHospital(request.surgeryOrAdmittedHospital())
            .wearsSpectacles(request.wearsSpectacles())
            .dentalTreatment(request.dentalTreatment())
            .build();
    }

    public void createMedicalHistory(String orgSlug, Long studentId, MedicalProfile request) {

    }
}
