package com.wexl.erp.fees.events;

import com.wexl.erp.fees.service.FeeHeadService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

@Slf4j
@AllArgsConstructor
@Component
public class FeeMasterEventListener implements ApplicationListener<FeeMasterCreatedEvent> {

  private final FeeHeadService feeHeadService;

  @Override
  public void onApplicationEvent(FeeMasterCreatedEvent event) {
    feeHeadService.createFeeHeads(event.getSource(), event.getRequest(), event.getOrgSlug());
  }
}
