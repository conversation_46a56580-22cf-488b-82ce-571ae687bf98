package com.wexl.erp.medical.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.retail.guardian.dto.GuardianRequest;
import lombok.Builder;

public record MedicalProfile() {

  @Builder
  public record Request(
      @JsonProperty("student_id") Long studentId,
      @JsonProperty("blood_group") String bloodGroup,
      @JsonProperty("height_cm") String height,
      @JsonProperty("weight_kg") String weight,
      @JsonProperty("drug_or_medicines") String drugOrMedicines,
      @JsonProperty("food_allergy") String foodAllergy,
      @JsonProperty("guardians") GuardianRequest guardians,
      @JsonProperty("allergies") Allergies allergies,
      @JsonProperty("illness") Illness illness,
      @JsonProperty("chronic_diseases") String chronicDiseases,
      @JsonProperty("heart_condition") String heartCondition,
      @JsonProperty("surgery_or_admitted_hospital") String surgeryOrAdmittedHospital,
      @JsonProperty("wears_spectacles") String wearsSpectacles,
      @JsonProperty("dental_treatment") String dentalTreatment,
      @JsonProperty("remarks") <PERSON><PERSON> remarks) {}

  @Builder
  public record Response(
      @JsonProperty("student_id") Long studentId,
      @JsonProperty("blood_group") String bloodGroup,
      @JsonProperty("height_cm") String height,
      @JsonProperty("weight_kg") String weight,
      @JsonProperty("drug_or_medicines") String drugOrMedicines,
      @JsonProperty("food_allergy") String foodAllergy,
      @JsonProperty("guardians") GuardianRequest guardians,
      @JsonProperty("allergies") Allergies allergies,
      @JsonProperty("illness") Illness illness,
      @JsonProperty("chronic_diseases") String chronicDiseases,
      @JsonProperty("heart_condition") String heartCondition,
      @JsonProperty("surgery_or_admitted_hospital") String surgeryOrAdmittedHospital,
      @JsonProperty("wears_spectacles") String wearsSpectacles,
      @JsonProperty("dental_treatment") String dentalTreatment,
      @JsonProperty("remarks") Remarks remarks) {}




  @Builder
  public record Allergies(
          @JsonProperty("bronchialAsthma") Boolean bronchialAsthma,
          @JsonProperty("headaches") Boolean headaches,
          @JsonProperty("vomitings") Boolean vomitings,
          @JsonProperty("urticaria") Boolean urticaria,
          @JsonProperty("convulsions") Boolean convulsions,
          @JsonProperty("tonsillitis") Boolean tonsillitis,
          @JsonProperty("anaemia") Boolean anaemia,
          @JsonProperty("sinusitis") Boolean sinusitis,
          @JsonProperty("bronchitis") Boolean bronchitis,
          @JsonProperty("tuberculosis") Boolean tuberculosis,
          @JsonProperty("pneumonia") Boolean pneumonia,
          @JsonProperty("epilepsyAnyOther") Boolean epilepsyAnyOther,
          @JsonProperty("anyOther") String anyOther) {}



  @Builder
  public record Illness(
          @JsonProperty("chickenpox") Boolean chickenpox,
          @JsonProperty("measles") Boolean measles,
          @JsonProperty("mumps") Boolean mumps,
          @JsonProperty("dizzinessFainting") Boolean dizzinessFainting,
          @JsonProperty("convulsionsDueToFever") Boolean convulsionsDueToFever,
          @JsonProperty("epilepsy") Boolean epilepsy,
          @JsonProperty("kidneyDisease") Boolean kidneyDisease,
          @JsonProperty("spondylitisOrthopaedicBackInjury") Boolean spondylitisOrthopaedicBackInjury,
          @JsonProperty("skinInfection") Boolean skinInfection) {}

  @Builder
  public record Remarks(
          @JsonProperty("dance") Boolean dance,
          @JsonProperty("aerobics") Boolean aerobics,
          @JsonProperty("yoga") Boolean yoga,
          @JsonProperty("pt") Boolean pt,
          @JsonProperty("games") Boolean games,
          @JsonProperty("camps") Boolean camps,
          @JsonProperty("treks") Boolean treks,
          @JsonProperty("athletics") Boolean athletics,
          @JsonProperty("longDistanceRuns") Boolean longDistanceRuns,
          @JsonProperty("gymnastics") Boolean gymnastics) {}
}
