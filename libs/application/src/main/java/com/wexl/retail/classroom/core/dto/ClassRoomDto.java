package com.wexl.retail.classroom.core.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Builder;

public record ClassRoomDto() {

  @Builder
  public record ClassRoomResponse(
      @JsonProperty("class_room_id") Long id,
      @JsonProperty("class_room_name") String classRoomName,
      @JsonProperty("teacher_id") List<Long> teacherId,
      @JsonProperty("teacher_name") List<String> teacherName,
      @JsonProperty("schedule_count") Integer scheduleCount) {}

  @Builder
  public record Extensions(String field1, String field2, String field3) {}

  @Builder
  public record Tutors(List<Long> tutorIds) {}

  public record ScheduleInstRequest(
      @JsonProperty("schedule_inst_id") Long scheduleInstId,
      @JsonProperty("meeting_room_id") Long meetingRoomId) {}
}
