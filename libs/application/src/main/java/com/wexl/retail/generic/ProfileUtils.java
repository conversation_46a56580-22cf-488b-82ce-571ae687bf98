package com.wexl.retail.generic;

import com.wexl.retail.util.Constants;
import java.util.List;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class ProfileUtils {
  @Value("${spring.profiles.active:default}")
  private String envProfile;

  public boolean isDev() {
    return List.of(Constants.DEV_PROFILE, Constants.LOCAL_PROFILE, Constants.ND_DEV_PROFILE)
        .contains(envProfile);
  }

  public boolean isTest() {
    return List.of(Constants.TEST_PROFILE).contains(envProfile);
  }
}
