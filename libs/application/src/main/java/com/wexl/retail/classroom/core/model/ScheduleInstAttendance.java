package com.wexl.retail.classroom.core.model;

import com.wexl.retail.erp.attendance.domain.CalenderDetails;
import com.wexl.retail.erp.attendance.domain.CompletionStatus;
import com.wexl.retail.model.Model;
import com.wexl.retail.model.Teacher;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;

@Data
@Builder
@RequiredArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "schedule_inst_attendance")
public class ScheduleInstAttendance extends Model {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private long id;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "teacher_id")
  private Teacher teacher;

  @OneToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "classroom_schedule_inst_id")
  private ClassroomScheduleInst classroomScheduleInst;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "calender_details_id")
  private CalenderDetails calenderDetails;

  @Column(name = "is_holiday")
  private CompletionStatus status = CompletionStatus.NOTCOMPLETED;

  @OneToMany(fetch = FetchType.LAZY, mappedBy = "scheduleInstAttendance", cascade = CascadeType.ALL)
  private List<ScheduleInstAttendanceDetails> scheduleInstAttendanceDetails;

  @Column(name = "org_slug")
  private String orgSlug;
}
